RESUME训练
cd /home/<USER>/work/lerobot-add_transformer && export http_proxy=http://localhost:7890 && export https_proxy=http://localhost:7890 && eval "$(conda shell.bash hook)" && conda activate lerobot && HF_ENDPOINT=https://hf-mirror.com python lerobot/scripts/train.py --config_path=outputs/train/diffusion_pusht_transformer_v2/checkpoints/last/pretrained_model/ --resume=true --wandb.enable=true --wandb.project=lerobot_dp --wandb.disable_artifact=true --steps=200000

开始训练
conda activate lerobot && HF_ENDPOINT=https://hf-mirror.com python lerobot/scripts/train.py --output_dir=outputs/train/diffusion_pusht_transformer_v2 --policy.type=diffusion --policy.repo_id=my_diffusion_pusht --dataset.repo_id=lerobot/pusht --dataset.video_backend=pyav --seed=100000 --env.type=pusht --batch_size=64 --steps=200000 --eval_freq=2500 --save_freq=5000 --wandb.enable=true --wandb.project=lerobot_dp --policy.device cuda --env.task=PushT-v0 --save_checkpoint true --job_name=diffusion_pusht_transformer --policy.type diffusion

# 🚀 MMDiT 200M参数训练命令 (推荐)
CUDA_VISIBLE_DEVICES=1 HF_ENDPOINT=https://hf-mirror.com PYTHONPATH=/home/<USER>/work/lerobot-add_transformer /home/<USER>/.conda/envs/lerobot/bin/python lerobot/scripts/train.py \
  --output_dir=outputs/train/diffusion_pusht_mmdit_200m_0706 \
  --policy.type=diffusion \
  --policy.use_transformer=true \
  --policy.use_mmdit=true \
  --policy.use_mmdit_dc=false \
  --policy.diffusion_step_embed_dim=640 \
  --policy.n_layer=18 \
  --policy.n_head=10 \
  --policy.n_dc_tokens=10 \
  --policy.n_dc_layers=14 \
  --policy.use_dc_t=false \
  --policy.vision_backbone=resnet101 \
  --policy.spatial_softmax_num_keypoints=64 \
  --dataset.repo_id=lerobot/pusht \
  --dataset.video_backend=pyav \
  --seed=100000 \
  --env.type=pusht \
  --env.task=PushT-v0 \
  --batch_size=64 \
  --steps=200000 \
  --eval_freq=5000 \
  --save_freq=10000 \
  --optimizer.lr=3e-5 \
  --optimizer.weight_decay=1e-4 \
  --scheduler.num_warmup_steps=2000 \
  --wandb.enable=true \
  --wandb.project=lerobot_dp \
  --policy.device=cuda \
  --save_checkpoint=true \
  --job_name=diffusion_pusht_mmdit \
  --wandb.disable_artifact=true

# 📊 模型规格：
# - 参数数量: ~191M (接近200M目标)
# - Hidden dimension: 640
# - Layers: 18
# - Attention heads: 10 (64 dim per head)
# - DC tokens: 10 tokens × 14 layers (SoftREPA)
# - Vision encoder: ResNet101
# - 批次大小: 32 (为大模型优化)
# - 学习率: 3e-5 (更保守)

# 🔧 简化版本（使用运行脚本）
python run_mmdit_200m_training.py --gpu 1 --batch_size 32 --steps 200000

# 原始14M参数命令（已修正）
CUDA_VISIBLE_DEVICES=1 HF_ENDPOINT=https://hf-mirror.com PYTHONPATH=/home/<USER>/work/lerobot-add_transformer /home/<USER>/.conda/envs/lerobot/bin/python lerobot/scripts/train.py \
  --output_dir=outputs/train/diffusion_pusht_mmdit_14m_0706 \
  --policy.type=diffusion \
  --policy.use_transformer=true \
  --policy.use_mmdit=true \
  --policy.use_mmdit_dc=true \
  --policy.n_dc_tokens=4 \
  --policy.n_dc_layers=6 \
  --policy.use_dc_t=true \
  --dataset.repo_id=lerobot/pusht \
  --dataset.video_backend=pyav \
  --seed=100000 \
  --env.type=pusht \
  --batch_size=64 \
  --steps=200000 \
  --eval_freq=2500 \
  --save_freq=10000 \
  --wandb.enable=true \
  --wandb.project=lerobot_dp \
  --policy.device=cuda \
  --env.task=PushT-v0 \
  --save_checkpoint=true \
  --job_name=diffusion_pusht_mmdit_14m \
  --wandb.disable_artifact=true

DIT训练命令
export HF_ENDPOINT=https://hf-mirror.com &&  export PYTHONPATH=/home/<USER>/work/lerobot-dit-flow && export http_proxy=http://localhost:7890 && export https_proxy=http://localhost:7890 &&  CUDA_VISIBLE_DEVICES=3  /home/<USER>/.conda/envs/lerobot/bin/python  lerobot/scripts/train.py --policy.type=ditflow --env.type=pusht --dataset.repo_id=lerobot/pusht --batch_size=64 --wandb.enable=true --offline.steps=200000 --job_name=ditflow_raw --online.steps=200000 --wandb.project=lerobot_dit --wandb.disable_artifact=true --online.steps_between_rollouts=1000 --online.env_seed=1000 --online.buffer_capacity=1000