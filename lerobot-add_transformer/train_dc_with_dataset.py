#!/usr/bin/env python

"""
Complete SoftREPA training script using lerobot dataset and safetensors checkpoint.
This script demonstrates the full workflow from checkpoint loading to DC token training.
"""

import argparse
import logging
import sys
import os
from pathlib import Path
import json
import time

# Add project root to path
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
from tqdm import tqdm
from safetensors.torch import load_file

# Import lerobot components
from lerobot.common.datasets.factory import make_dataset
from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    ContrastiveLoss,
    SoftREPATrainer,
    TransformerDCInference
)
from lerobot.common.utils.utils import init_logging, get_safe_torch_device


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('dc_training.log')
        ]
    )


def create_dataset_config(dataset_repo_id: str):
    """
    Create a minimal dataset configuration for lerobot.

    Args:
        dataset_repo_id: Dataset repository ID (e.g., "lerobot/pusht")

    Returns:
        Configuration object for dataset loading
    """
    from lerobot.configs.default import DatasetConfig
    from omegaconf import OmegaConf

    # Create a proper DatasetConfig
    dataset_config = DatasetConfig(
        repo_id=dataset_repo_id,
        root=None,  # Use default cache location
        revision=None,
        image_transforms=OmegaConf.create({
            "enable": False,  # Disable image transforms for simplicity
        })
    )

    # Wrap in the expected structure with complete policy config
    config = OmegaConf.create({
        "dataset": dataset_config,
        "policy": {
            "n_obs_steps": 2,  # Required for delta timestamps
            "n_action_steps": 16,  # Match the model's horizon
            "horizon": 16,  # Explicitly set horizon
            "observation_delta_indices": None,  # Required field
            "action_delta_indices": None,  # Required field
            "reward_delta_indices": None,  # Required field
        },
        "training": True,
    })

    return config


def load_and_create_dc_model(checkpoint_path: str, dataset, n_dc_tokens: int = 4, n_dc_layers: int = 6):
    """
    Load safetensors checkpoint and create DiffusionModel_DC with proper dataset configuration.

    Args:
        checkpoint_path: Path to checkpoint directory
        dataset: Loaded lerobot dataset
        n_dc_tokens: Number of DC tokens per layer
        n_dc_layers: Number of layers to apply DC tokens

    Returns:
        DiffusionModel_DC instance
    """
    checkpoint_dir = Path(checkpoint_path)

    # Load checkpoint config
    config_path = checkpoint_dir / "pretrained_model" / "config.json"
    with open(config_path, 'r') as f:
        checkpoint_config = json.load(f)

    # 从检查点配置中提取基本信息
    action_dim = checkpoint_config.get("output_features", {}).get("action", {}).get("shape", [2])[0]
    state_dim = checkpoint_config.get("input_features", {}).get("observation.state", {}).get("shape", [2])[0]
    has_images = "observation.image" in checkpoint_config.get("input_features", {})

    # 从safetensors文件中推断实际的条件维度
    safetensors_path = checkpoint_dir / "pretrained_model" / "model.safetensors"
    from safetensors.torch import load_file
    state_dict = load_file(str(safetensors_path))

    # 从cond_obs_emb.weight推断条件维度
    cond_obs_weight_key = "diffusion.net.cond_obs_emb.weight"
    if cond_obs_weight_key in state_dict:
        actual_cond_dim = state_dict[cond_obs_weight_key].shape[1]
        logging.info(f"Detected condition dimension from pretrained model: {actual_cond_dim}")
    else:
        actual_cond_dim = state_dim + (3 * 96 * 96 if has_images else 0)  # fallback
        logging.warning(f"Could not detect condition dimension, using fallback: {actual_cond_dim}")

    # 检测encoder层数
    encoder_layers = len([name for name in state_dict.keys() if "diffusion.net.encoder.layers." in name and ".self_attn.in_proj_weight" in name])
    logging.info(f"Detected encoder layers: {encoder_layers}")

    # 使用检查点的完整配置，确保维度匹配
    config_dict = {
        # 基本结构
        "horizon": checkpoint_config.get("horizon", 16),
        "n_obs_steps": checkpoint_config.get("n_obs_steps", 2),
        "n_action_steps": checkpoint_config.get("n_action_steps", 8),

        # Transformer设置 - 使用检查点的实际配置
        "use_transformer": True,
        "n_layer": checkpoint_config.get("n_layer", 12),  # decoder layers
        "n_cond_layers": encoder_layers,  # encoder layers from safetensors
        "n_head": checkpoint_config.get("n_head", 16),
        "p_drop_emb": checkpoint_config.get("p_drop_emb", 0.0),
        "p_drop_attn": checkpoint_config.get("p_drop_attn", 0.3),
        "diffusion_step_embed_dim": checkpoint_config.get("diffusion_step_embed_dim", 1024),

        # Vision设置
        "vision_backbone": checkpoint_config.get("vision_backbone", "resnet18"),
        "crop_shape": tuple(checkpoint_config.get("crop_shape", [84, 84])),
        "use_group_norm": checkpoint_config.get("use_group_norm", True),
        "spatial_softmax_num_keypoints": checkpoint_config.get("spatial_softmax_num_keypoints", 32),
        "use_separate_rgb_encoder_per_camera": checkpoint_config.get("use_separate_rgb_encoder_per_camera", True),

        # 噪声调度器设置
        "noise_scheduler_type": checkpoint_config.get("noise_scheduler_type", "DDPM"),
        "num_train_timesteps": checkpoint_config.get("num_train_timesteps", 100),
        "beta_schedule": checkpoint_config.get("beta_schedule", "squaredcos_cap_v2"),
        "prediction_type": checkpoint_config.get("prediction_type", "epsilon"),
    }

    # 创建配置
    config = DiffusionConfig(**config_dict)

    # 添加输入输出特征配置（从预训练模型配置中获取）
    if hasattr(checkpoint_config, 'input_features') and checkpoint_config.input_features:
        config.input_features = checkpoint_config.input_features
    else:
        # 根据检测到的特征创建input_features
        config.input_features = {}
        config.input_features['observation.state'] = {'shape': [state_dim], 'type': 'STATE'}
        if has_images:
            # 从预训练配置中获取图像形状，或使用默认值
            image_shape = checkpoint_config.get('input_features', {}).get('observation.image', {}).get('shape', [3, 96, 96])
            config.input_features['observation.image'] = {'shape': image_shape, 'type': 'VISUAL'}

    if hasattr(checkpoint_config, 'output_features') and checkpoint_config.output_features:
        config.output_features = checkpoint_config.output_features
    else:
        config.output_features = {'action': {'shape': [action_dim], 'type': 'ACTION'}}

    logging.info(f"Created DiffusionConfig:")
    logging.info(f"  Action dim: {action_dim}")
    logging.info(f"  State dim: {state_dim}")
    logging.info(f"  Has images: {has_images}")
    logging.info(f"  Condition dim: {actual_cond_dim}")
    logging.info(f"  Encoder layers: {encoder_layers}")
    logging.info(f"  Decoder layers: {config.n_layer}")
    logging.info(f"  Input features: {config.input_features}")
    logging.info(f"  Output features: {config.output_features}")

    # Create DiffusionModel_DC from checkpoint with correct condition dimension
    # We need to pass the actual condition dimension detected from the pretrained model
    diffusion_model = DiffusionModel_DC.from_pretrained(
        pretrained_model_path=checkpoint_path,
        config=config,
        n_dc_tokens=n_dc_tokens,
        n_dc_layers=n_dc_layers,
        use_dc_t=True,
        cond_dim=actual_cond_dim  # Pass the detected condition dimension
    )

    return diffusion_model


def train_dc_tokens(
    diffusion_model: DiffusionModel_DC,
    dataloader: DataLoader,
    device: torch.device,
    learning_rate: float = 1e-4,
    steps: int = 10000,
    log_freq: int = 100,
    save_freq: int = 1000,
    output_dir: str = "./dc_checkpoints"
):
    """
    Train DC tokens using contrastive learning.
    
    Args:
        diffusion_model: DiffusionModel_DC instance
        dataloader: Training data loader
        device: Training device
        learning_rate: Learning rate for DC token training
        steps: Number of training steps
        log_freq: Logging frequency
        save_freq: Checkpoint saving frequency
        output_dir: Output directory for checkpoints
    """
    # Move model to device
    diffusion_model.to(device)
    
    # Freeze base model and get DC parameters
    diffusion_model.freeze_base_model()
    dc_params = diffusion_model.get_dc_parameters()
    
    if not dc_params:
        raise ValueError("No DC parameters found! Check DC token setup.")
    
    logging.info(f"Training {len(dc_params)} DC parameters")
    diffusion_model.print_parameter_stats()
    
    # Create optimizer for DC parameters only
    optimizer = torch.optim.AdamW(dc_params, lr=learning_rate, weight_decay=1e-3)
    
    # Create contrastive learning components
    trainer = SoftREPATrainer(diffusion_model)
    contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device=device.type)
    
    # Create gradient scaler for mixed precision
    scaler = GradScaler()
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Training loop
    logging.info("Starting DC token training...")
    diffusion_model.train()
    
    # Create infinite data iterator
    from itertools import cycle
    data_iter = cycle(dataloader)
    
    progress_bar = tqdm(range(steps), desc="Training DC tokens")
    
    for step in progress_bar:
        # Get batch
        batch = next(data_iter)

        # Debug: Print batch structure on first iteration
        if step == 0:
            logging.info("Batch structure:")
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    logging.info(f"  {key}: {value.shape} {value.dtype}")
                else:
                    logging.info(f"  {key}: {type(value)}")

        # Move batch to device
        for key in batch:
            if isinstance(batch[key], torch.Tensor):
                batch[key] = batch[key].to(device, non_blocking=True)
        
        # Forward pass with mixed precision
        with autocast():
            # Compute contrastive error matrix
            error_matrix = trainer(batch, use_dc=True)
            
            # Compute contrastive loss
            loss = contrastive_loss(error_matrix)
        
        # Backward pass
        optimizer.zero_grad()
        scaler.scale(loss).backward()
        
        # Gradient clipping
        scaler.unscale_(optimizer)
        torch.nn.utils.clip_grad_norm_(dc_params, 1.0)
        
        # Optimizer step
        scaler.step(optimizer)
        scaler.update()
        
        # Compute metrics
        with torch.no_grad():
            batch_size = error_matrix.shape[0]
            diagonal_errors = torch.diag(error_matrix)
            off_diagonal_errors = error_matrix[~torch.eye(batch_size, dtype=bool, device=error_matrix.device)]
            accuracy = (diagonal_errors.mean() < off_diagonal_errors.mean()).float()
        
        # Update progress bar
        progress_bar.set_postfix({
            'loss': f"{loss.item():.4f}",
            'acc': f"{accuracy.item():.3f}",
            'diag': f"{diagonal_errors.mean().item():.4f}",
            'off_diag': f"{off_diagonal_errors.mean().item():.4f}"
        })
        
        # Logging
        if (step + 1) % log_freq == 0:
            logging.info(
                f"Step {step+1}/{steps}: "
                f"Loss={loss.item():.4f}, "
                f"Acc={accuracy.item():.3f}, "
                f"DiagErr={diagonal_errors.mean().item():.4f}, "
                f"OffDiagErr={off_diagonal_errors.mean().item():.4f}"
            )
        
        # Save checkpoint
        if (step + 1) % save_freq == 0:
            checkpoint_path = Path(output_dir) / f"dc_checkpoint_step_{step+1}.pth"
            torch.save({
                'step': step + 1,
                'model_state_dict': diffusion_model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': loss.item(),
                'accuracy': accuracy.item(),
            }, checkpoint_path)
            logging.info(f"Checkpoint saved: {checkpoint_path}")
    
    # Save final model
    final_path = Path(output_dir) / "dc_model_final.pth"
    torch.save({
        'step': steps,
        'model_state_dict': diffusion_model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss.item(),
        'accuracy': accuracy.item(),
    }, final_path)
    logging.info(f"Final model saved: {final_path}")


def main():
    parser = argparse.ArgumentParser(description="Train DiffusionModel_DC with lerobot dataset")
    
    parser.add_argument("--checkpoint", type=str, required=True,
                       help="Path to lerobot checkpoint directory")
    parser.add_argument("--dataset", type=str, default="lerobot/pusht",
                       help="Dataset repository ID")
    parser.add_argument("--n_dc_tokens", type=int, default=4,
                       help="Number of DC tokens per layer")
    parser.add_argument("--n_dc_layers", type=int, default=6,
                       help="Number of layers to apply DC tokens")
    parser.add_argument("--device", type=str, default="cuda",
                       help="Device to use for training")
    parser.add_argument("--batch_size", type=int, default=64,
                       help="Training batch size")
    parser.add_argument("--learning_rate", type=float, default=1e-4,
                       help="Learning rate for DC token training")
    parser.add_argument("--steps", type=int, default=10000,
                       help="Number of training steps")
    parser.add_argument("--log_freq", type=int, default=100,
                       help="Logging frequency")
    parser.add_argument("--save_freq", type=int, default=1000,
                       help="Checkpoint saving frequency")
    parser.add_argument("--output_dir", type=str, default="./dc_checkpoints",
                       help="Output directory for checkpoints")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    init_logging()
    
    logging.info("=" * 60)
    logging.info("SoftREPA DC Token Training with Lerobot Dataset")
    logging.info("=" * 60)
    
    # Validate inputs
    if not Path(args.checkpoint).exists():
        logging.error(f"Checkpoint directory not found: {args.checkpoint}")
        return
    
    # Get device
    device = get_safe_torch_device(args.device, log=True)
    
    logging.info(f"Configuration:")
    logging.info(f"  Checkpoint: {args.checkpoint}")
    logging.info(f"  Dataset: {args.dataset}")
    logging.info(f"  DC tokens: {args.n_dc_tokens} tokens, {args.n_dc_layers} layers")
    logging.info(f"  Device: {device}")
    logging.info(f"  Batch size: {args.batch_size}")
    logging.info(f"  Learning rate: {args.learning_rate}")
    logging.info(f"  Training steps: {args.steps}")
    
    try:
        # Create dataset config
        dataset_config = create_dataset_config(args.dataset)
        
        # Load dataset
        logging.info("Loading dataset...")

        # Set up delta_timestamps for sequence loading
        delta_timestamps = {
            # Load 2 observation steps: previous and current
            "observation.state": [-0.1, 0.0],
            "observation.image": [-0.1, 0.0],
            # Load 16 action steps: current and 15 future actions
            "action": [i * 0.1 for i in range(16)],  # [0.0, 0.1, 0.2, ..., 1.5]
        }

        # Create LeRobotDataset directly
        from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
        dataset = LeRobotDataset(
            repo_id=dataset_config.dataset.repo_id,
            delta_timestamps=delta_timestamps,
            image_transforms=None,  # No image transforms for now
        )
        logging.info(f"Dataset loaded: {len(dataset)} samples")
        
        # Create dataloader
        dataloader = DataLoader(
            dataset,
            batch_size=args.batch_size,
            shuffle=True,
            num_workers=4,
            pin_memory=device.type != "cpu",
            drop_last=True
        )
        
        # Load model from checkpoint
        logging.info("Loading DiffusionModel_DC from checkpoint...")
        diffusion_model = load_and_create_dc_model(
            args.checkpoint,
            dataset,
            args.n_dc_tokens,
            args.n_dc_layers
        )
        
        # Train DC tokens
        train_dc_tokens(
            diffusion_model=diffusion_model,
            dataloader=dataloader,
            device=device,
            learning_rate=args.learning_rate,
            steps=args.steps,
            log_freq=args.log_freq,
            save_freq=args.save_freq,
            output_dir=args.output_dir
        )
        
        logging.info("Training completed successfully!")
        
    except Exception as e:
        logging.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
