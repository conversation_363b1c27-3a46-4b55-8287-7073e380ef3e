#!/usr/bin/env python

# Copyright 2024 Columbia Artificial Intelligence, Robotics Lab,
# and The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Diffusion Policy as per "Diffusion Policy: Visuomotor Policy Learning via Action Diffusion"

TODO(alexander-soare):
  - Remove reliance on diffusers for DDPMScheduler and LR scheduler.
"""
import pdb
import copy
import math
from collections import deque
from typing import Callable, Tuple

import einops
import numpy as np
import torch
import torch.nn.functional as F  # noqa: N812
import torchvision
from diffusers.schedulers.scheduling_ddim import DDIMScheduler
from diffusers.schedulers.scheduling_ddpm import DDPMScheduler
from torch import Tensor, nn

from lerobot.common.constants import OBS_ENV, OBS_ROBOT
from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.dit_policy import DiTPolicyForDiffusion
from lerobot.common.policies.normalize import Normalize, Unnormalize
from lerobot.common.policies.pretrained import PreTrainedPolicy
from lerobot.common.policies.utils import (
    get_device_from_parameters,
    get_dtype_from_parameters,
    get_output_shape,
    populate_queues,
)


class DiffusionPolicy(PreTrainedPolicy):
    """
    Diffusion Policy as per "Diffusion Policy: Visuomotor Policy Learning via Action Diffusion"
    (paper: https://arxiv.org/abs/2303.04137, code: https://github.com/real-stanford/diffusion_policy).
    """

    config_class = DiffusionConfig
    name = "diffusion"

    def __init__(
        self,
        config: DiffusionConfig,
        dataset_stats: dict[str, dict[str, Tensor]] | None = None,
    ):
        """
        Args:
            config: Policy configuration class instance or None, in which case the default instantiation of
                the configuration class is used.
            dataset_stats: Dataset statistics to be used for normalization. If not passed here, it is expected
                that they will be passed with a call to `load_state_dict` before the policy is used.
        """
        super().__init__(config)
        config.validate_features()
        self.config = config

        self.normalize_inputs = Normalize(config.input_features, config.normalization_mapping, dataset_stats)
        self.normalize_targets = Normalize(
            config.output_features, config.normalization_mapping, dataset_stats
        )
        self.unnormalize_outputs = Unnormalize(
            config.output_features, config.normalization_mapping, dataset_stats
        )

        # queues are populated during rollout of the policy, they contain the n latest observations and actions
        self._queues = None

        self.diffusion = DiffusionModel(config)

        self.reset()

    def get_optim_params(self) -> dict:
        return self.diffusion.parameters()

    def reset(self):
        """Clear observation and action queues. Should be called on `env.reset()`"""
        self._queues = {
            "observation.state": deque(maxlen=self.config.n_obs_steps),
            "action": deque(maxlen=self.config.n_action_steps),
        }
        if self.config.image_features:
            self._queues["observation.images"] = deque(maxlen=self.config.n_obs_steps)
        if self.config.env_state_feature:
            self._queues["observation.environment_state"] = deque(maxlen=self.config.n_obs_steps)

    @torch.no_grad
    def select_action(self, batch: dict[str, Tensor]) -> Tensor:
        """Select a single action given environment observations.

        This method handles caching a history of observations and an action trajectory generated by the
        underlying diffusion model. Here's how it works:
          - `n_obs_steps` steps worth of observations are cached (for the first steps, the observation is
            copied `n_obs_steps` times to fill the cache).
          - The diffusion model generates `horizon` steps worth of actions.
          - `n_action_steps` worth of actions are actually kept for execution, starting from the current step.
        Schematically this looks like:
            ----------------------------------------------------------------------------------------------
            (legend: o = n_obs_steps, h = horizon, a = n_action_steps)
            |timestep            | n-o+1 | n-o+2 | ..... | n     | ..... | n+a-1 | n+a   | ..... | n-o+h |
            |observation is used | YES   | YES   | YES   | YES   | NO    | NO    | NO    | NO    | NO    |
            |action is generated | YES   | YES   | YES   | YES   | YES   | YES   | YES   | YES   | YES   |
            |action is used      | NO    | NO    | NO    | YES   | YES   | YES   | NO    | NO    | NO    |
            ----------------------------------------------------------------------------------------------
        Note that this means we require: `n_action_steps <= horizon - n_obs_steps + 1`. Also, note that
        "horizon" may not the best name to describe what the variable actually means, because this period is
        actually measured from the first observation which (if `n_obs_steps` > 1) happened in the past.
        """
        batch = self.normalize_inputs(batch)
        if self.config.image_features:
            batch = dict(batch)  # shallow copy so that adding a key doesn't modify the original
            batch["observation.images"] = torch.stack(
                [batch[key] for key in self.config.image_features], dim=-4
            )
        # Note: It's important that this happens after stacking the images into a single key.
        self._queues = populate_queues(self._queues, batch)

        if len(self._queues["action"]) == 0:
            # stack n latest observations from the queue
            batch = {k: torch.stack(list(self._queues[k]), dim=1) for k in batch if k in self._queues}
            actions = self.diffusion.generate_actions(batch)

            # TODO(rcadene): make above methods return output dictionary?
            actions = self.unnormalize_outputs({"action": actions})["action"]

            self._queues["action"].extend(actions.transpose(0, 1))

        action = self._queues["action"].popleft()
        return action

    def forward(self, batch: dict[str, Tensor]) -> tuple[Tensor, None]:
        """Run the batch through the model and compute the loss for training or validation."""
        batch = self.normalize_inputs(batch)
        if self.config.image_features:
            batch = dict(batch)  # shallow copy so that adding a key doesn't modify the original
            batch["observation.images"] = torch.stack(
                [batch[key] for key in self.config.image_features], dim=-4
            )
        batch = self.normalize_targets(batch)
        loss = self.diffusion.compute_loss(batch)
        # no output_dict so returning None
        return loss, None


def _make_noise_scheduler(name: str, **kwargs: dict) -> DDPMScheduler | DDIMScheduler:
    """
    Factory for noise scheduler instances of the requested type. All kwargs are passed
    to the scheduler.
    """
    if name == "DDPM":
        return DDPMScheduler(**kwargs)
    elif name == "DDIM":
        return DDIMScheduler(**kwargs)
    else:
        raise ValueError(f"Unsupported noise scheduler type {name}")


class DiffusionModel(nn.Module):
    def __init__(self, config: DiffusionConfig):
        super().__init__()
        self.config = config

        # Build observation encoders (depending on which observations are provided).
        global_cond_dim = self.config.robot_state_feature.shape[0]
        if self.config.image_features:
            num_images = len(self.config.image_features)
            if self.config.use_separate_rgb_encoder_per_camera:
                encoders = [DiffusionRgbEncoder(config) for _ in range(num_images)]
                self.rgb_encoder = nn.ModuleList(encoders)
                global_cond_dim += encoders[0].feature_dim * num_images
            else:
                self.rgb_encoder = DiffusionRgbEncoder(config)
                global_cond_dim += self.rgb_encoder.feature_dim * num_images
        if self.config.env_state_feature:
            global_cond_dim += self.config.env_state_feature.shape[0]
        '''这里确定使用transformer还是unet'''
        if config.use_transformer:
            # Check architecture type
            if hasattr(config, 'use_dit') and config.use_dit:
                # Complete DiT (Diffusion Transformer) architecture from dit-policy
                # Includes progressive encoders and full original implementation
                self.net = DiTForDiffusion(config, cond_dim=global_cond_dim * config.n_obs_steps)
            elif hasattr(config, 'use_mmdit') and config.use_mmdit:
                if hasattr(config, 'use_mmdit_dc') and config.use_mmdit_dc:
                    # MMDiT with SoftREPA-style DC tokens
                    # MMDiT expects flattened condition dim like UNet
                    self.net = MMDiTForDiffusion_DC(
                        config,
                        cond_dim=global_cond_dim * config.n_obs_steps,
                        n_dc_tokens=getattr(config, 'n_dc_tokens', 4),
                        n_dc_layers=getattr(config, 'n_dc_layers', 6),
                        use_dc_t=getattr(config, 'use_dc_t', True)
                    )
                else:
                    # Standard MMDiT
                    # MMDiT expects flattened condition dim like UNet
                    self.net = MMDiTForDiffusion(config, cond_dim=global_cond_dim * config.n_obs_steps)
            else:
                # Original Transformer uses per-step conditioning
                self.net = TransformerForDiffusion(config, cond_dim=global_cond_dim)
        else:
            self.net = DiffusionConditionalUnet1d(
                config, global_cond_dim=global_cond_dim * config.n_obs_steps
            )

        self.noise_scheduler = _make_noise_scheduler(
            config.noise_scheduler_type,
            num_train_timesteps=config.num_train_timesteps,
            beta_start=config.beta_start,
            beta_end=config.beta_end,
            beta_schedule=config.beta_schedule,
            clip_sample=config.clip_sample,
            clip_sample_range=config.clip_sample_range,
            prediction_type=config.prediction_type,
        )

        if config.num_inference_steps is None:
            self.num_inference_steps = self.noise_scheduler.config.num_train_timesteps
        else:
            self.num_inference_steps = config.num_inference_steps

    # ========= inference  ============
    def conditional_sample(
        self, batch_size: int, global_cond: Tensor | None = None, generator: torch.Generator | None = None
    ) -> Tensor:
        device = get_device_from_parameters(self)
        dtype = get_dtype_from_parameters(self)

        # Sample prior.
        sample = torch.randn(
            size=(batch_size, self.config.horizon, self.config.action_feature.shape[0]),
            dtype=dtype,
            device=device,
            generator=generator,
        )

        self.noise_scheduler.set_timesteps(self.num_inference_steps)

        for t in self.noise_scheduler.timesteps:
            # Predict model output.
            model_output = self.net(
                sample,
                torch.full(sample.shape[:1], t, dtype=torch.long, device=sample.device),
                global_cond=global_cond,
            )
            # Compute previous image: x_t -> x_t-1
            sample = self.noise_scheduler.step(model_output, t, sample, generator=generator).prev_sample

        return sample

    def _prepare_global_conditioning(self, batch: dict[str, Tensor]) -> Tensor:
        """Encode image features and concatenate them all together along with the state vector."""
        batch_size, n_obs_steps = batch[OBS_ROBOT].shape[:2]
        global_cond_feats = [batch[OBS_ROBOT]]

        # Special handling for DiT policy - return raw observations for internal processing
        if self.net.__class__.__name__ == "DiTPolicyForDiffusion":
            # DiT policy handles observation processing internally with progressive encoders
            # Return raw observation batch for complete dit-policy architecture
            raw_obs = {}
            for key, value in batch.items():
                if key.startswith("observation."):
                    raw_obs[key] = value
            return raw_obs

        # Extract image features.
        if self.config.image_features:
            if self.config.use_separate_rgb_encoder_per_camera:
                # Combine batch and sequence dims while rearranging to make the camera index dimension first.
                images_per_camera = einops.rearrange(batch["observation.images"], "b s n ... -> n (b s) ...")
                img_features_list = torch.cat(
                    [
                        encoder(images)
                        for encoder, images in zip(self.rgb_encoder, images_per_camera, strict=True)
                    ]
                )
                # Separate batch and sequence dims back out. The camera index dim gets absorbed into the
                # feature dim (effectively concatenating the camera features).
                img_features = einops.rearrange(
                    img_features_list, "(n b s) ... -> b s (n ...)", b=batch_size, s=n_obs_steps
                )
            else:
                # Combine batch, sequence, and "which camera" dims before passing to shared encoder.
                img_features = self.rgb_encoder(
                    einops.rearrange(batch["observation.images"], "b s n ... -> (b s n) ...")
                )
                # Separate batch dim and sequence dim back out. The camera index dim gets absorbed into the
                # feature dim (effectively concatenating the camera features).
                img_features = einops.rearrange(
                    img_features, "(b s n) ... -> b s (n ...)", b=batch_size, s=n_obs_steps
                )
            global_cond_feats.append(img_features)

        if self.config.env_state_feature:
            global_cond_feats.append(batch[OBS_ENV])

        # Concatenate features then flatten to (B, global_cond_dim).
        return torch.cat(global_cond_feats, dim=-1).flatten(start_dim=1)

    def generate_actions(self, batch: dict[str, Tensor]) -> Tensor:
        """
        This function expects `batch` to have:
        {
            "observation.state": (B, n_obs_steps, state_dim)

            "observation.images": (B, n_obs_steps, num_cameras, C, H, W)
                AND/OR
            "observation.environment_state": (B, environment_dim)
        }
        """
        batch_size, n_obs_steps = batch["observation.state"].shape[:2]
        assert n_obs_steps == self.config.n_obs_steps

        # Encode image features and concatenate them all together along with the state vector.
        global_cond = self._prepare_global_conditioning(batch)  # (B, global_cond_dim)

        # run sampling
        actions = self.conditional_sample(batch_size, global_cond=global_cond)

        # Extract `n_action_steps` steps worth of actions (from the current observation).
        start = n_obs_steps - 1
        end = start + self.config.n_action_steps
        actions = actions[:, start:end]

        return actions

    def compute_loss(self, batch: dict[str, Tensor]) -> Tensor:
        """
        This function expects `batch` to have (at least):
        {
            "observation.state": (B, n_obs_steps, state_dim)

            "observation.images": (B, n_obs_steps, num_cameras, C, H, W)
                AND/OR
            "observation.environment_state": (B, environment_dim)

            "action": (B, horizon, action_dim)
            "action_is_pad": (B, horizon)
        }
        """
        # Input validation.
        assert set(batch).issuperset({"observation.state", "action", "action_is_pad"})
        assert "observation.images" in batch or "observation.environment_state" in batch
        n_obs_steps = batch["observation.state"].shape[1]
        horizon = batch["action"].shape[1]
        assert horizon == self.config.horizon
        assert n_obs_steps == self.config.n_obs_steps

        # Encode image features and concatenate them all together along with the state vector.
        global_cond = self._prepare_global_conditioning(batch)  # (B, global_cond_dim)

        # Forward diffusion.
        trajectory = batch["action"]
        # Sample noise to add to the trajectory.
        eps = torch.randn(trajectory.shape, device=trajectory.device)
        # Sample a random noising timestep for each item in the batch.
        timesteps = torch.randint(
            low=0,
            high=self.noise_scheduler.config.num_train_timesteps,
            size=(trajectory.shape[0],),
            device=trajectory.device,
        ).long()
        # Add noise to the clean trajectories according to the noise magnitude at each timestep.
        noisy_trajectory = self.noise_scheduler.add_noise(trajectory, eps, timesteps)

        # Run the denoising network (that might denoise the trajectory, or attempt to predict the noise).
        pred = self.net(noisy_trajectory, timesteps, global_cond=global_cond)

        # Compute the loss.
        # The target is either the original trajectory, or the noise.
        if self.config.prediction_type == "epsilon":
            target = eps
        elif self.config.prediction_type == "sample":
            target = batch["action"]
        else:
            raise ValueError(f"Unsupported prediction type {self.config.prediction_type}")

        loss = F.mse_loss(pred, target, reduction="none")

        # Mask loss wherever the action is padded with copies (edges of the dataset trajectory).
        if self.config.do_mask_loss_for_padding:
            if "action_is_pad" not in batch:
                raise ValueError(
                    "You need to provide 'action_is_pad' in the batch when "
                    f"{self.config.do_mask_loss_for_padding=}."
                )
            in_episode_bound = ~batch["action_is_pad"]
            loss = loss * in_episode_bound.unsqueeze(-1)

        return loss.mean()


class SpatialSoftmax(nn.Module):
    """
    Spatial Soft Argmax operation described in "Deep Spatial Autoencoders for Visuomotor Learning" by Finn et al.
    (https://arxiv.org/pdf/1509.06113). A minimal port of the robomimic implementation.

    At a high level, this takes 2D feature maps (from a convnet/ViT) and returns the "center of mass"
    of activations of each channel, i.e., keypoints in the image space for the policy to focus on.

    Example: take feature maps of size (512x10x12). We generate a grid of normalized coordinates (10x12x2):
    -----------------------------------------------------
    | (-1., -1.)   | (-0.82, -1.)   | ... | (1., -1.)   |
    | (-1., -0.78) | (-0.82, -0.78) | ... | (1., -0.78) |
    | ...          | ...            | ... | ...         |
    | (-1., 1.)    | (-0.82, 1.)    | ... | (1., 1.)    |
    -----------------------------------------------------
    This is achieved by applying channel-wise softmax over the activations (512x120) and computing the dot
    product with the coordinates (120x2) to get expected points of maximal activation (512x2).

    The example above results in 512 keypoints (corresponding to the 512 input channels). We can optionally
    provide num_kp != None to control the number of keypoints. This is achieved by a first applying a learnable
    linear mapping (in_channels, H, W) -> (num_kp, H, W).
    """

    def __init__(self, input_shape, num_kp=None):
        """
        Args:
            input_shape (list): (C, H, W) input feature map shape.
            num_kp (int): number of keypoints in output. If None, output will have the same number of channels as input.
        """
        super().__init__()

        assert len(input_shape) == 3
        self._in_c, self._in_h, self._in_w = input_shape

        if num_kp is not None:
            self.nets = torch.nn.Conv2d(self._in_c, num_kp, kernel_size=1)
            self._out_c = num_kp
        else:
            self.nets = None
            self._out_c = self._in_c

        # we could use torch.linspace directly but that seems to behave slightly differently than numpy
        # and causes a small degradation in pc_success of pre-trained models.
        pos_x, pos_y = np.meshgrid(np.linspace(-1.0, 1.0, self._in_w), np.linspace(-1.0, 1.0, self._in_h))
        pos_x = torch.from_numpy(pos_x.reshape(self._in_h * self._in_w, 1)).float()
        pos_y = torch.from_numpy(pos_y.reshape(self._in_h * self._in_w, 1)).float()
        # register as buffer so it's moved to the correct device.
        self.register_buffer("pos_grid", torch.cat([pos_x, pos_y], dim=1))

    def forward(self, features: Tensor) -> Tensor:
        """
        Args:
            features: (B, C, H, W) input feature maps.
        Returns:
            (B, K, 2) image-space coordinates of keypoints.
        """
        if self.nets is not None:
            features = self.nets(features)

        # [B, K, H, W] -> [B * K, H * W] where K is number of keypoints
        features = features.reshape(-1, self._in_h * self._in_w)
        # 2d softmax normalization
        attention = F.softmax(features, dim=-1)
        # [B * K, H * W] x [H * W, 2] -> [B * K, 2] for spatial coordinate mean in x and y dimensions
        expected_xy = attention @ self.pos_grid
        # reshape to [B, K, 2]
        feature_keypoints = expected_xy.view(-1, self._out_c, 2)

        return feature_keypoints


class DiffusionRgbEncoder(nn.Module):
    """Encodes an RGB image into a 1D feature vector.

    Includes the ability to normalize and crop the image first.
    """

    def __init__(self, config: DiffusionConfig):
        super().__init__()
        # Set up optional preprocessing.
        if config.crop_shape is not None:
            self.do_crop = True
            # Always use center crop for eval
            self.center_crop = torchvision.transforms.CenterCrop(config.crop_shape)
            if config.crop_is_random:
                self.maybe_random_crop = torchvision.transforms.RandomCrop(config.crop_shape)
            else:
                self.maybe_random_crop = self.center_crop
        else:
            self.do_crop = False

        # Set up backbone.
        backbone_model = getattr(torchvision.models, config.vision_backbone)(
            weights=config.pretrained_backbone_weights
        )
        # Note: This assumes that the layer4 feature map is children()[-3]
        # TODO(alexander-soare): Use a safer alternative.
        self.backbone = nn.Sequential(*(list(backbone_model.children())[:-2]))
        if config.use_group_norm:
            if config.pretrained_backbone_weights:
                raise ValueError(
                    "You can't replace BatchNorm in a pretrained model without ruining the weights!"
                )
            self.backbone = _replace_submodules(
                root_module=self.backbone,
                predicate=lambda x: isinstance(x, nn.BatchNorm2d),
                func=lambda x: nn.GroupNorm(num_groups=x.num_features // 16, num_channels=x.num_features),
            )

        # Set up pooling and final layers.
        # Use a dry run to get the feature map shape.
        # The dummy input should take the number of image channels from `config.image_features` and it should
        # use the height and width from `config.crop_shape` if it is provided, otherwise it should use the
        # height and width from `config.image_features`.

        # Note: we have a check in the config class to make sure all images have the same shape.
        images_shape = next(iter(config.image_features.values())).shape
        dummy_shape_h_w = config.crop_shape if config.crop_shape is not None else images_shape[1:]
        dummy_shape = (1, images_shape[0], *dummy_shape_h_w)
        feature_map_shape = get_output_shape(self.backbone, dummy_shape)[1:]

        self.pool = SpatialSoftmax(feature_map_shape, num_kp=config.spatial_softmax_num_keypoints)
        self.feature_dim = config.spatial_softmax_num_keypoints * 2
        self.out = nn.Linear(config.spatial_softmax_num_keypoints * 2, self.feature_dim)
        self.relu = nn.ReLU()

    def forward(self, x: Tensor) -> Tensor:
        """
        Args:
            x: (B, C, H, W) image tensor with pixel values in [0, 1].
        Returns:
            (B, D) image feature.
        """
        # Preprocess: maybe crop (if it was set up in the __init__).
        if self.do_crop:
            if self.training:  # noqa: SIM108
                x = self.maybe_random_crop(x)
            else:
                # Always use center crop for eval.
                x = self.center_crop(x)
        # Extract backbone feature.
        x = torch.flatten(self.pool(self.backbone(x)), start_dim=1)
        # Final linear layer with non-linearity.
        x = self.relu(self.out(x))
        return x


def _replace_submodules(
    root_module: nn.Module, predicate: Callable[[nn.Module], bool], func: Callable[[nn.Module], nn.Module]
) -> nn.Module:
    """
    Args:
        root_module: The module for which the submodules need to be replaced
        predicate: Takes a module as an argument and must return True if the that module is to be replaced.
        func: Takes a module as an argument and returns a new module to replace it with.
    Returns:
        The root module with its submodules replaced.
    """
    if predicate(root_module):
        return func(root_module)

    replace_list = [k.split(".") for k, m in root_module.named_modules(remove_duplicate=True) if predicate(m)]
    for *parents, k in replace_list:
        parent_module = root_module
        if len(parents) > 0:
            parent_module = root_module.get_submodule(".".join(parents))
        if isinstance(parent_module, nn.Sequential):
            src_module = parent_module[int(k)]
        else:
            src_module = getattr(parent_module, k)
        tgt_module = func(src_module)
        if isinstance(parent_module, nn.Sequential):
            parent_module[int(k)] = tgt_module
        else:
            setattr(parent_module, k, tgt_module)
    # verify that all BN are replaced
    assert not any(predicate(m) for _, m in root_module.named_modules(remove_duplicate=True))
    return root_module


class DiffusionSinusoidalPosEmb(nn.Module):
    """1D sinusoidal positional embeddings as in Attention is All You Need."""

    def __init__(self, dim: int):
        super().__init__()
        self.dim = dim

    def forward(self, x: Tensor) -> Tensor:
        device = x.device
        half_dim = self.dim // 2
        emb = math.log(10000) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, device=device) * -emb)
        emb = x.unsqueeze(-1) * emb.unsqueeze(0)
        emb = torch.cat((emb.sin(), emb.cos()), dim=-1)
        return emb


class DiffusionConv1dBlock(nn.Module):
    """Conv1d --> GroupNorm --> Mish"""

    def __init__(self, inp_channels, out_channels, kernel_size, n_groups=8):
        super().__init__()

        self.block = nn.Sequential(
            nn.Conv1d(inp_channels, out_channels, kernel_size, padding=kernel_size // 2),
            nn.GroupNorm(n_groups, out_channels),
            nn.Mish(),
        )

    def forward(self, x):
        return self.block(x)


class DiffusionConditionalUnet1d(nn.Module):
    """A 1D convolutional UNet with FiLM modulation for conditioning.

    Note: this removes local conditioning as compared to the original diffusion policy code.
    """

    def __init__(self, config: DiffusionConfig, global_cond_dim: int):
        super().__init__()

        self.config = config

        # Encoder for the diffusion timestep.
        self.diffusion_step_encoder = nn.Sequential(
            DiffusionSinusoidalPosEmb(config.diffusion_step_embed_dim),
            nn.Linear(config.diffusion_step_embed_dim, config.diffusion_step_embed_dim * 4),
            nn.Mish(),
            nn.Linear(config.diffusion_step_embed_dim * 4, config.diffusion_step_embed_dim),
        )

        # The FiLM conditioning dimension.
        cond_dim = config.diffusion_step_embed_dim + global_cond_dim

        # In channels / out channels for each downsampling block in the Unet's encoder. For the decoder, we
        # just reverse these.
        in_out = [(config.action_feature.shape[0], config.down_dims[0])] + list(
            zip(config.down_dims[:-1], config.down_dims[1:], strict=True)
        )

        # Unet encoder.
        common_res_block_kwargs = {
            "cond_dim": cond_dim,
            "kernel_size": config.kernel_size,
            "n_groups": config.n_groups,
            "use_film_scale_modulation": config.use_film_scale_modulation,
        }
        self.down_modules = nn.ModuleList([])
        for ind, (dim_in, dim_out) in enumerate(in_out):
            is_last = ind >= (len(in_out) - 1)
            self.down_modules.append(
                nn.ModuleList(
                    [
                        DiffusionConditionalResidualBlock1d(dim_in, dim_out, **common_res_block_kwargs),
                        DiffusionConditionalResidualBlock1d(dim_out, dim_out, **common_res_block_kwargs),
                        # Downsample as long as it is not the last block.
                        nn.Conv1d(dim_out, dim_out, 3, 2, 1) if not is_last else nn.Identity(),
                    ]
                )
            )

        # Processing in the middle of the auto-encoder.
        self.mid_modules = nn.ModuleList(
            [
                DiffusionConditionalResidualBlock1d(
                    config.down_dims[-1], config.down_dims[-1], **common_res_block_kwargs
                ),
                DiffusionConditionalResidualBlock1d(
                    config.down_dims[-1], config.down_dims[-1], **common_res_block_kwargs
                ),
            ]
        )

        # Unet decoder.
        self.up_modules = nn.ModuleList([])
        for ind, (dim_out, dim_in) in enumerate(reversed(in_out[1:])):
            is_last = ind >= (len(in_out) - 1)
            self.up_modules.append(
                nn.ModuleList(
                    [
                        # dim_in * 2, because it takes the encoder's skip connection as well
                        DiffusionConditionalResidualBlock1d(dim_in * 2, dim_out, **common_res_block_kwargs),
                        DiffusionConditionalResidualBlock1d(dim_out, dim_out, **common_res_block_kwargs),
                        # Upsample as long as it is not the last block.
                        nn.ConvTranspose1d(dim_out, dim_out, 4, 2, 1) if not is_last else nn.Identity(),
                    ]
                )
            )

        self.final_conv = nn.Sequential(
            DiffusionConv1dBlock(config.down_dims[0], config.down_dims[0], kernel_size=config.kernel_size),
            nn.Conv1d(config.down_dims[0], config.action_feature.shape[0], 1),
        )

    def forward(self, x: Tensor, timestep: Tensor | int, global_cond=None) -> Tensor:
        """
        Args:
            x: (B, T, input_dim) tensor for input to the Unet.
            timestep: (B,) tensor of (timestep_we_are_denoising_from - 1).
            global_cond: (B, global_cond_dim)
            output: (B, T, input_dim)
        Returns:
            (B, T, input_dim) diffusion model prediction.
        """
        # For 1D convolutions we'll need feature dimension first.
        x = einops.rearrange(x, "b t d -> b d t")

        timesteps_embed = self.diffusion_step_encoder(timestep)

        # If there is a global conditioning feature, concatenate it to the timestep embedding.
        if global_cond is not None:
            global_feature = torch.cat([timesteps_embed, global_cond], axis=-1)
        else:
            global_feature = timesteps_embed

        # Run encoder, keeping track of skip features to pass to the decoder.
        encoder_skip_features: list[Tensor] = []
        for resnet, resnet2, downsample in self.down_modules:
            x = resnet(x, global_feature)
            x = resnet2(x, global_feature)
            encoder_skip_features.append(x)
            x = downsample(x)

        for mid_module in self.mid_modules:
            x = mid_module(x, global_feature)

        # Run decoder, using the skip features from the encoder.
        for resnet, resnet2, upsample in self.up_modules:
            x = torch.cat((x, encoder_skip_features.pop()), dim=1)
            x = resnet(x, global_feature)
            x = resnet2(x, global_feature)
            x = upsample(x)

        x = self.final_conv(x)

        x = einops.rearrange(x, "b d t -> b t d")
        return x


class DiffusionConditionalResidualBlock1d(nn.Module):
    """ResNet style 1D convolutional block with FiLM modulation for conditioning."""

    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        cond_dim: int,
        kernel_size: int = 3,
        n_groups: int = 8,
        # Set to True to do scale modulation with FiLM as well as bias modulation (defaults to False meaning
        # FiLM just modulates bias).
        use_film_scale_modulation: bool = False,
    ):
        super().__init__()

        self.use_film_scale_modulation = use_film_scale_modulation
        self.out_channels = out_channels

        self.conv1 = DiffusionConv1dBlock(in_channels, out_channels, kernel_size, n_groups=n_groups)

        # FiLM modulation (https://arxiv.org/abs/1709.07871) outputs per-channel bias and (maybe) scale.
        cond_channels = out_channels * 2 if use_film_scale_modulation else out_channels
        self.cond_encoder = nn.Sequential(nn.Mish(), nn.Linear(cond_dim, cond_channels))

        self.conv2 = DiffusionConv1dBlock(out_channels, out_channels, kernel_size, n_groups=n_groups)

        # A final convolution for dimension matching the residual (if needed).
        self.residual_conv = (
            nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else nn.Identity()
        )

    def forward(self, x: Tensor, cond: Tensor) -> Tensor:
        """
        Args:
            x: (B, in_channels, T)
            cond: (B, cond_dim)
        Returns:
            (B, out_channels, T)
        """
        out = self.conv1(x)

        # Get condition embedding. Unsqueeze for broadcasting to `out`, resulting in (B, out_channels, 1).
        cond_embed = self.cond_encoder(cond).unsqueeze(-1)
        if self.use_film_scale_modulation:
            # Treat the embedding as a list of scales and biases.
            scale = cond_embed[:, : self.out_channels]
            bias = cond_embed[:, self.out_channels :]
            out = scale * out + bias
        else:
            # Treat the embedding as biases.
            out = out + cond_embed

        out = self.conv2(out)
        out = out + self.residual_conv(x)
        return out


class _TransformerEncoder(nn.Module):
    def __init__(self, base_module, num_layers):
        super().__init__()
        self.layers = nn.ModuleList(
            [copy.deepcopy(base_module) for _ in range(num_layers)]
        )

    def forward(self, src):
        x, outputs = src, []
        for layer in self.layers:
            x = layer(x)
            outputs.append(x)
        return outputs
    
class _TransformerDecoder(nn.Module):
    def __init__(self, base_module, num_layers):
        super().__init__()
        self.layers = nn.ModuleList([copy.deepcopy(base_module) for _ in range(num_layers)])

    def forward(self, tgt, memory, tgt_mask=None, memory_mask=None, tgt_key_padding_mask=None, memory_key_padding_mask=None):
        x = tgt
        for layer, single_cond in zip(self.layers, memory):
            x = layer(
                x,
                single_cond,
                tgt_mask=tgt_mask,
                memory_mask=memory_mask,
                tgt_key_padding_mask=tgt_key_padding_mask,
                memory_key_padding_mask=memory_key_padding_mask,
            )
        return x

class TransformerForDiffusion(nn.Module):
    def __init__(self, config: DiffusionConfig, cond_dim: int):
        super().__init__()
        self.config = config

        # conditioning dimension used for positional embeddings
        # conditioning over input observation steps (n_obs_steps) + time (1)
        t_cond = 1 + config.n_obs_steps

        input_dim = config.action_feature.shape[0]
        # input embedding stem
        self.input_emb = nn.Linear(input_dim, config.diffusion_step_embed_dim)
        self.pos_emb = nn.Parameter(torch.zeros(1, config.horizon, config.diffusion_step_embed_dim))
        self.drop = nn.Dropout(config.p_drop_emb)

        # cond encoder
        self.time_emb = DiffusionSinusoidalPosEmb(config.diffusion_step_embed_dim)

        self.cond_obs_emb = nn.Linear(cond_dim, config.diffusion_step_embed_dim)
        self.encoder = None

        self.cond_pos_emb = nn.Parameter(torch.zeros(1, t_cond, config.diffusion_step_embed_dim))
        if config.n_cond_layers > 0:
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=config.diffusion_step_embed_dim,
                nhead=config.n_head,
                dim_feedforward=4 * config.diffusion_step_embed_dim,
                dropout=config.p_drop_attn,
                activation="gelu",
                batch_first=True,
                norm_first=True,
            )
            self.encoder = _TransformerEncoder(encoder_layer, config.n_cond_layers)
        else:
            self.encoder = nn.Sequential(
                nn.Linear(config.diffusion_step_embed_dim, 4 * config.diffusion_step_embed_dim),
                nn.Mish(),
                nn.Linear(4 * config.diffusion_step_embed_dim, config.diffusion_step_embed_dim),
            )
        # decoder
        decoder_layer = nn.TransformerDecoderLayer(
            d_model=config.diffusion_step_embed_dim,
            nhead=config.n_head,
            dim_feedforward=4 * config.diffusion_step_embed_dim,
            dropout=config.p_drop_attn,
            activation="gelu",
            batch_first=True,
            norm_first=True,  # important for stability
        )
        self.decoder = _TransformerDecoder(decoder_layer, config.n_layer)

        # attention mask
        if config.causal_attn:
            # causal mask to ensure that attention is only applied to the left in the input sequence
            # torch.nn.Transformer uses additive mask as opposed to multiplicative mask in minGPT
            # therefore, the upper triangle should be -inf and others (including diag) should be 0.
            sz = config.horizon
            mask = (torch.triu(torch.ones(sz, sz)) == 1).transpose(0, 1)
            mask = mask.float().masked_fill(mask == 0, float("-inf")).masked_fill(mask == 1, float(0.0))
            self.register_buffer("mask", mask)

            # assume conditioning over time and observation both
            p, q = torch.meshgrid(torch.arange(config.horizon), torch.arange(t_cond), indexing="ij")
            mask = p >= (q - 1)  # add one dimension since time is the first token in cond
            mask = mask.float().masked_fill(mask == 0, float("-inf")).masked_fill(mask == 1, float(0.0))
            self.register_buffer("memory_mask", mask)
        else:
            self.mask = None
            self.memory_mask = None

        # decoder head
        self.ln_f = nn.LayerNorm(config.diffusion_step_embed_dim)
        self.head = nn.Linear(config.diffusion_step_embed_dim, input_dim)

        # constants
        self.t_cond = t_cond
        self.horizon = config.horizon
        self.n_obs_steps = config.n_obs_steps

        # init
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """
        Initializes weights for different network layers in the module.
            - nn.Linear and nn.Embedding: Normal(0, 0.02) for weights, zero for bias.
            - nn.MultiheadAttention: Normal(0, 0.02) for projection weights, zero for biases.
            - nn.LayerNorm: Ones for weights, zeros for biases.
            - Normal(0, 0.02) for positional embeddings module.pos_emb.
            - Predefined layers are ignored.
        Args:
            module (torch.nn.Module): The module to initialize.
        """
        ignore_types = (
            nn.Dropout,
            DiffusionSinusoidalPosEmb,
            nn.TransformerEncoderLayer,
            nn.TransformerDecoderLayer,
            nn.TransformerEncoder,
            _TransformerEncoder,
            _TransformerDecoder,
            nn.TransformerDecoder,
            nn.ModuleList,
            nn.Mish,
            nn.Sequential,
        )
        if isinstance(module, (nn.Linear, nn.Embedding)):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if isinstance(module, nn.Linear) and module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.MultiheadAttention):
            weight_names = ["in_proj_weight", "q_proj_weight", "k_proj_weight", "v_proj_weight"]
            for name in weight_names:
                weight = getattr(module, name)
                if weight is not None:
                    torch.nn.init.normal_(weight, mean=0.0, std=0.02)

            bias_names = ["in_proj_bias", "bias_k", "bias_v"]
            for name in bias_names:
                bias = getattr(module, name)
                if bias is not None:
                    torch.nn.init.zeros_(bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
        elif isinstance(module, TransformerForDiffusion):
            torch.nn.init.normal_(module.pos_emb, mean=0.0, std=0.02)
            if module.cond_obs_emb is not None:
                torch.nn.init.normal_(module.cond_pos_emb, mean=0.0, std=0.02)
        elif isinstance(module, ignore_types):
            # no param
            pass
        else:
            raise RuntimeError("Unaccounted module {}".format(module))

    def get_optim_groups(self, weight_decay: float = 1e-3):
        """
        This long function is unfortunately doing something very simple and is being very defensive:
        We are separating out all parameters of the model into two buckets: those that will experience
        weight decay for regularization and those that won't (biases, and layernorm/embedding weights).
        We are then returning the PyTorch optimizer object.
        """

        # separate out all parameters to those that will and won't experience regularizing weight decay
        decay = set()
        no_decay = set()
        whitelist_weight_modules = (torch.nn.Linear, torch.nn.MultiheadAttention)
        blacklist_weight_modules = (torch.nn.LayerNorm, torch.nn.Embedding)
        for mn, m in self.named_modules():
            for pn, _ in m.named_parameters():
                fpn = "{}.{}".format(mn, pn) if mn else pn  # full param name

                if pn.endswith("bias"):
                    # all biases will not be decayed
                    no_decay.add(fpn)
                elif pn.startswith("bias"):
                    # MultiheadAttention bias starts with "bias"
                    no_decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, whitelist_weight_modules):
                    # weights of whitelist modules will be weight decayed
                    decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, blacklist_weight_modules):
                    # weights of blacklist modules will NOT be weight decayed
                    no_decay.add(fpn)

        # special case the position embedding parameter in the root GPT module as not decayed
        no_decay.add("pos_emb")
        # no_decay.add("_dummy_variable")
        if self.cond_pos_emb is not None:
            no_decay.add("cond_pos_emb")

        # validate that we considered every parameter
        # param_dict = {pn: p for pn, p in self.named_parameters()}
        param_dict = dict(self.named_parameters())
        inter_params = decay & no_decay
        union_params = decay | no_decay
        assert len(inter_params) == 0, "parameters {} made it into both decay/no_decay sets!".format(
            str(inter_params)
        )
        assert (
            len(param_dict.keys() - union_params) == 0
        ), "parameters {} were not separated into either decay/no_decay set!".format(
            str(param_dict.keys() - union_params),
        )

        # create the pytorch optimizer object
        optim_groups = [
            {
                "params": [param_dict[pn] for pn in sorted(decay)],
                "weight_decay": weight_decay,
            },
            {
                "params": [param_dict[pn] for pn in sorted(no_decay)],
                "weight_decay": 0.0,
            },
        ]
        return optim_groups

    def configure_optimizers(
        self,
        learning_rate: float = 1e-4,
        weight_decay: float = 1e-3,
        betas: Tuple[float, float] = (0.9, 0.95),
    ):
        optim_groups = self.get_optim_groups(weight_decay=weight_decay)
        optimizer = torch.optim.AdamW(optim_groups, lr=learning_rate, betas=betas)
        return optimizer

    def forward(self, sample: torch.Tensor, timestep: torch.Tensor, global_cond: torch.Tensor, **kwargs):
        """
        Args:
            sample: (B, T, input_dim) tensor for input to the decoder after embedding.
            timestep: (B,) tensor of (timestep_we_are_denoising_from - 1).
            global_cond: (B, global_cond_dim)
            output: (B, T, input_dim)
        Returns:
            (B, T, input_dim) diffusion model prediction.
        """
        # 1. time
        batch_size = sample.shape[0]
        time_emb = self.time_emb(timestep).unsqueeze(1)  # (B,1,n_emb)

        cond = einops.rearrange(
            global_cond, "b (s n) ... -> b s (n ...)", b=batch_size, s=self.n_obs_steps
        )  # (B,To,n_cond)

        # process input
        input_emb = self.input_emb(sample)

        # encoder
        cond_obs_emb = self.cond_obs_emb(cond)  # (B,To,n_emb)
        cond_embeddings = torch.cat([time_emb, cond_obs_emb], dim=1)  # (B,To + 1,n_emb)

        position_embeddings = self.cond_pos_emb[
            :, : cond_embeddings.shape[1], :
        ]  # each position maps to a (learnable) vector
        memory = self.drop(cond_embeddings + position_embeddings)
        memory = self.encoder(memory)  # (B,T_cond,n_emb)

        # decoder
        position_embeddings = self.pos_emb[
            :, : input_emb.shape[1], :
        ]  # each position maps to a (learnable) vector
        x = self.drop(input_emb + position_embeddings)  # (B,T,n_emb)
        x = self.decoder(
            tgt=x, memory=memory, tgt_mask=self.mask, memory_mask=self.memory_mask
        )  # (B,T,n_emb)

        # head
        x = self.ln_f(x)
        x = self.head(x)  # (B,T,n_inp)

        return x


class TransformerForDiffusion_DC(nn.Module):
    def __init__(self, config: DiffusionConfig, cond_dim: int,n_dc_tokens: int = 4, n_dc_layers: int = 6, use_dc_t: bool = False, use_dc: bool = False):
        super().__init__()
        self.config = config

        # conditioning dimension used for positional embeddings
        # conditioning over input observation steps (n_obs_steps) + time (1)
        t_cond = 1 + config.n_obs_steps

        input_dim = config.action_feature.shape[0]
        # input embedding stem
        self.input_emb = nn.Linear(input_dim, config.diffusion_step_embed_dim)
        self.pos_emb = nn.Parameter(torch.zeros(1, config.horizon, config.diffusion_step_embed_dim))
        self.drop = nn.Dropout(config.p_drop_emb)

        # cond encoder
        self.time_emb = DiffusionSinusoidalPosEmb(config.diffusion_step_embed_dim)

        self.cond_obs_emb = nn.Linear(cond_dim, config.diffusion_step_embed_dim)
        self.encoder = None

        self.cond_pos_emb = nn.Parameter(torch.zeros(1, t_cond, config.diffusion_step_embed_dim))
        if config.n_cond_layers > 0:
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=config.diffusion_step_embed_dim,
                nhead=config.n_head,
                dim_feedforward=4 * config.diffusion_step_embed_dim,
                dropout=config.p_drop_attn,
                activation="gelu",
                batch_first=True,
                norm_first=True,
            )
            self.encoder = _TransformerEncoder(encoder_layer, config.n_cond_layers)
        else:
            self.encoder = nn.Sequential(
                nn.Linear(config.diffusion_step_embed_dim, 4 * config.diffusion_step_embed_dim),
                nn.Mish(),
                nn.Linear(4 * config.diffusion_step_embed_dim, config.diffusion_step_embed_dim),
            )
        # decoder
        decoder_layer = nn.TransformerDecoderLayer(
            d_model=config.diffusion_step_embed_dim,
            nhead=config.n_head,
            dim_feedforward=4 * config.diffusion_step_embed_dim,
            dropout=config.p_drop_attn,
            activation="gelu",
            batch_first=True,
            norm_first=True,  # important for stability
        )
        self.decoder = _TransformerDecoder(decoder_layer, config.n_layer)

        # attention mask
        if config.causal_attn:
            # causal mask to ensure that attention is only applied to the left in the input sequence
            # torch.nn.Transformer uses additive mask as opposed to multiplicative mask in minGPT
            # therefore, the upper triangle should be -inf and others (including diag) should be 0.
            sz = config.horizon
            mask = (torch.triu(torch.ones(sz, sz)) == 1).transpose(0, 1)
            mask = mask.float().masked_fill(mask == 0, float("-inf")).masked_fill(mask == 1, float(0.0))
            self.register_buffer("mask", mask)

            # assume conditioning over time and observation both
            p, q = torch.meshgrid(torch.arange(config.horizon), torch.arange(t_cond), indexing="ij")
            mask = p >= (q - 1)  # add one dimension since time is the first token in cond
            mask = mask.float().masked_fill(mask == 0, float("-inf")).masked_fill(mask == 1, float(0.0))
            self.register_buffer("memory_mask", mask)
        else:
            self.mask = None
            self.memory_mask = None

        # decoder head
        self.ln_f = nn.LayerNorm(config.diffusion_step_embed_dim)
        self.head = nn.Linear(config.diffusion_step_embed_dim, input_dim)

        # constants
        self.t_cond = t_cond
        self.horizon = config.horizon
        self.n_obs_steps = config.n_obs_steps

        # init
        self.apply(self._init_weights)

        #dc_tokens
        self.n_dc_tokens = n_dc_tokens
        self.n_dc_layers = n_dc_layers
        self.use_dc_t = use_dc_t
        self.use_dc = use_dc
        self.dc_tokens = nn.Parameter(torch.randn(self.n_dc_layers, self.n_dc_tokens, config.diffusion_step_embed_dim))
        nn.init.normal_(self.dc_tokens, mean=0, std=0.02)
        if self.use_dc_t:
            # Time-dependent DC tokens (discretized timesteps)
            self.dc_t_tokens = nn.Embedding(100, config.diffusion_step_embed_dim*self.n_dc_layers)
            nn.init.normal_(self.dc_t_tokens.weight, mean=0, std=0.02)
        else:
            self.dc_t_tokens = None


    def _init_weights(self, module):
        """
        Initializes weights for different network layers in the module.
            - nn.Linear and nn.Embedding: Normal(0, 0.02) for weights, zero for bias.
            - nn.MultiheadAttention: Normal(0, 0.02) for projection weights, zero for biases.
            - nn.LayerNorm: Ones for weights, zeros for biases.
            - Normal(0, 0.02) for positional embeddings module.pos_emb.
            - Predefined layers are ignored.
        Args:
            module (torch.nn.Module): The module to initialize.
        """
        ignore_types = (
            nn.Dropout,
            DiffusionSinusoidalPosEmb,
            nn.TransformerEncoderLayer,
            nn.TransformerDecoderLayer,
            nn.TransformerEncoder,
            _TransformerEncoder,
            _TransformerDecoder,
            nn.TransformerDecoder,
            nn.ModuleList,
            nn.Mish,
            nn.Sequential,
            
            
        )
        if isinstance(module, (nn.Linear, nn.Embedding)):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if isinstance(module, nn.Linear) and module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.MultiheadAttention):
            weight_names = ["in_proj_weight", "q_proj_weight", "k_proj_weight", "v_proj_weight"]
            for name in weight_names:
                weight = getattr(module, name)
                if weight is not None:
                    torch.nn.init.normal_(weight, mean=0.0, std=0.02)

            bias_names = ["in_proj_bias", "bias_k", "bias_v"]
            for name in bias_names:
                bias = getattr(module, name)
                if bias is not None:
                    torch.nn.init.zeros_(bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
        elif isinstance(module, TransformerForDiffusion):
            torch.nn.init.normal_(module.pos_emb, mean=0.0, std=0.02)
            if module.cond_obs_emb is not None:
                torch.nn.init.normal_(module.cond_pos_emb, mean=0.0, std=0.02)
        elif isinstance(module, ignore_types):
            # no param
            pass
        else:
            raise RuntimeError("Unaccounted module {}".format(module))

    def get_optim_groups(self, weight_decay: float = 1e-3):
        """
        This long function is unfortunately doing something very simple and is being very defensive:
        We are separating out all parameters of the model into two buckets: those that will experience
        weight decay for regularization and those that won't (biases, and layernorm/embedding weights).
        We are then returning the PyTorch optimizer object.
        """

        # separate out all parameters to those that will and won't experience regularizing weight decay
        decay = set()
        no_decay = set()
        whitelist_weight_modules = (torch.nn.Linear, torch.nn.MultiheadAttention)
        blacklist_weight_modules = (torch.nn.LayerNorm, torch.nn.Embedding)
        for mn, m in self.named_modules():
            for pn, _ in m.named_parameters():
                fpn = "{}.{}".format(mn, pn) if mn else pn  # full param name

                if pn.endswith("bias"):
                    # all biases will not be decayed
                    no_decay.add(fpn)
                elif pn.startswith("bias"):
                    # MultiheadAttention bias starts with "bias"
                    no_decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, whitelist_weight_modules):
                    # weights of whitelist modules will be weight decayed
                    decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, blacklist_weight_modules):
                    # weights of blacklist modules will NOT be weight decayed
                    no_decay.add(fpn)

        # special case the position embedding parameter in the root GPT module as not decayed
        no_decay.add("pos_emb")
        # no_decay.add("_dummy_variable")
        if self.cond_pos_emb is not None:
            no_decay.add("cond_pos_emb")

        # validate that we considered every parameter
        # param_dict = {pn: p for pn, p in self.named_parameters()}
        param_dict = dict(self.named_parameters())
        inter_params = decay & no_decay
        union_params = decay | no_decay
        assert len(inter_params) == 0, "parameters {} made it into both decay/no_decay sets!".format(
            str(inter_params)
        )
        assert (
            len(param_dict.keys() - union_params) == 0
        ), "parameters {} were not separated into either decay/no_decay set!".format(
            str(param_dict.keys() - union_params),
        )

        # create the pytorch optimizer object
        optim_groups = [
            {
                "params": [param_dict[pn] for pn in sorted(decay)],
                "weight_decay": weight_decay,
            },
            {
                "params": [param_dict[pn] for pn in sorted(no_decay)],
                "weight_decay": 0.0,
            },
        ]
        return optim_groups

    def configure_optimizers(
        self,
        learning_rate: float = 1e-4,
        weight_decay: float = 1e-3,
        betas: Tuple[float, float] = (0.9, 0.95),
    ):
        optim_groups = self.get_optim_groups(weight_decay=weight_decay)
        optimizer = torch.optim.AdamW(optim_groups, lr=learning_rate, betas=betas)
        return optimizer

    def forward(self, sample: torch.Tensor, timestep: torch.Tensor, global_cond: torch.Tensor, **kwargs):
        """
        Args:
            sample: (B, T, input_dim) tensor for input to the decoder after embedding.
            timestep: (B,) tensor of (timestep_we_are_denoising_from - 1).
            global_cond: (B, global_cond_dim)
            output: (B, T, input_dim)
        Returns:
            (B, T, input_dim) diffusion model prediction.
        """
        # 1. time
        batch_size = sample.shape[0]
        time_emb = self.time_emb(timestep).unsqueeze(1)  # (B,1,n_emb)

        cond = einops.rearrange(
            global_cond, "b (s n) ... -> b s (n ...)", b=batch_size, s=self.n_obs_steps
        )  # (B,To,n_cond)

        # process input
        input_emb = self.input_emb(sample)

        # encoder
        cond_obs_emb = self.cond_obs_emb(cond)  # (B,To,n_emb)
        cond_embeddings = torch.cat([time_emb, cond_obs_emb], dim=1)  # (B,To + 1,n_emb)

        position_embeddings = self.cond_pos_emb[
            :, : cond_embeddings.shape[1], :
        ]  # each position maps to a (learnable) vector
        memory = self.drop(cond_embeddings + position_embeddings)
        memory = self.encoder(memory)  # (n_layers,B,T_cond,n_emb)
        '''dc token应该在什么时候加进条件里面去，在未进入encoder之前加还是加在处理完成后的memory上'''
        n=cond_embeddings.size(-2)
        if self.use_dc_t:
            int_t = timestep.type(torch.int32)
            if torch.sum(int_t>=1000)>0: # index supported [0, 999]
                int_t-=1
            dctemb = self.dc_t_tokens(int_t//10).contiguous().to(hidden_states.device)
            dctemb = dctemb.chunk(self.n_dc_layers, dim=-1)
        if self.use_dc:
            for i in range(self.n_dc_layers):
                dc1 = self.dc_tokens[i].expand(memory[i].shape[0], -1, -1)
                if dc1.shape[-1] == self.config.diffusion_step_embed_dim:
                    dc1 = self.cond_obs_emb(dc1)
                if self.use_dc_t:
                    dc2 = self.dc_t_tokens(i).unsqueeze(1).expand(-1, self.n_dc_tokens, -1)
                    dc_add = dc1 + dc2
                else:
                    dc_add = dc1
                memory[i] = torch.cat([dc_add.type(memory[i].dtype), memory[i]], dim=1) # (B,S+N,C)

        # decoder
        position_embeddings = self.pos_emb[
            :, : input_emb.shape[1], :
        ]  # each position maps to a (learnable) vector
        x = self.drop(input_emb + position_embeddings)  # (B,T,n_emb)
        x = self.decoder(
            tgt=x, memory=memory, tgt_mask=self.mask, memory_mask=self.memory_mask
        )  # (B,T,n_emb)

        # head
        x = self.ln_f(x)
        x = self.head(x)  # (B,T,n_inp)

        return x


class MMDiTBlock(nn.Module):
    """
    MMDiT block with separate weights for action and condition modalities.
    Similar to SD3's approach but adapted for robot action diffusion.
    """
    def __init__(self, hidden_dim: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads

        # Separate normalization for action and condition
        self.action_norm1 = nn.LayerNorm(hidden_dim)
        self.action_norm2 = nn.LayerNorm(hidden_dim)
        self.cond_norm1 = nn.LayerNorm(hidden_dim)
        self.cond_norm2 = nn.LayerNorm(hidden_dim)

        # Joint attention (action and condition interact)
        self.joint_attn = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )

        # Separate MLPs for action and condition
        self.action_mlp = nn.Sequential(
            nn.Linear(hidden_dim, 4 * hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(4 * hidden_dim, hidden_dim),
            nn.Dropout(dropout)
        )

        self.cond_mlp = nn.Sequential(
            nn.Linear(hidden_dim, 4 * hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(4 * hidden_dim, hidden_dim),
            nn.Dropout(dropout)
        )

    def forward(self, action_tokens: torch.Tensor, cond_tokens: torch.Tensor):
        """
        Args:
            action_tokens: (B, T_action, hidden_dim)
            cond_tokens: (B, T_cond, hidden_dim)
        Returns:
            Updated action_tokens and cond_tokens
        """
        # Normalize inputs
        action_norm = self.action_norm1(action_tokens)
        cond_norm = self.cond_norm1(cond_tokens)

        # Concatenate for joint attention
        joint_tokens = torch.cat([action_norm, cond_norm], dim=1)  # (B, T_action + T_cond, hidden_dim)

        # Joint attention
        attn_out, _ = self.joint_attn(joint_tokens, joint_tokens, joint_tokens)

        # Split back to action and condition
        T_action = action_tokens.shape[1]
        action_attn = attn_out[:, :T_action, :]
        cond_attn = attn_out[:, T_action:, :]

        # Residual connections for attention
        action_tokens = action_tokens + action_attn
        cond_tokens = cond_tokens + cond_attn

        # MLP processing with separate weights
        action_tokens = action_tokens + self.action_mlp(self.action_norm2(action_tokens))
        cond_tokens = cond_tokens + self.cond_mlp(self.cond_norm2(cond_tokens))

        return action_tokens, cond_tokens


class MMDiTForDiffusion(nn.Module):
    """
    Multimodal Diffusion Transformer (MMDiT) for robot action diffusion.
    Inspired by SD3's MMDiT architecture with separate weights for action and condition modalities.
    """
    def __init__(self, config: DiffusionConfig, cond_dim: int):
        super().__init__()
        self.config = config
        self.hidden_dim = config.diffusion_step_embed_dim
        self.num_layers = config.n_layer
        self.num_heads = config.n_head

        # Action sequence processing
        input_dim = config.action_feature.shape[0]
        self.action_proj = nn.Linear(input_dim, self.hidden_dim)
        self.action_pos_emb = nn.Parameter(torch.zeros(1, config.horizon, self.hidden_dim))

        # Condition processing (observations + time)
        self.time_emb = DiffusionSinusoidalPosEmb(self.hidden_dim)
        self.cond_proj = nn.Linear(cond_dim, self.hidden_dim)

        # MMDiT blocks with separate weights for action and condition
        self.mmdit_blocks = nn.ModuleList([
            MMDiTBlock(
                hidden_dim=self.hidden_dim,
                num_heads=self.num_heads,
                dropout=config.p_drop_attn
            ) for _ in range(self.num_layers)
        ])

        # Final output layer
        self.norm_out = nn.LayerNorm(self.hidden_dim)
        self.proj_out = nn.Linear(self.hidden_dim, input_dim)

        # Dropout
        self.dropout = nn.Dropout(config.p_drop_emb)

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
        elif hasattr(module, 'action_pos_emb'):
            torch.nn.init.normal_(module.action_pos_emb, mean=0.0, std=0.02)

    def forward(self, sample: torch.Tensor, timestep: torch.Tensor, global_cond: torch.Tensor, **kwargs):
        """
        Args:
            sample: (B, T, action_dim) noisy action sequence
            timestep: (B,) diffusion timestep
            global_cond: (B, cond_dim) global conditioning (observations)
        Returns:
            (B, T, action_dim) predicted noise or denoised actions
        """
        # Process action tokens
        action_tokens = self.action_proj(sample)  # (B, T, hidden_dim)
        action_tokens = action_tokens + self.action_pos_emb[:, :action_tokens.shape[1], :]
        action_tokens = self.dropout(action_tokens)

        # Process condition tokens
        time_emb = self.time_emb(timestep)  # (B, hidden_dim)
        cond_tokens = self.cond_proj(global_cond)  # (B, hidden_dim)

        # Combine time and condition
        cond_tokens = cond_tokens + time_emb  # (B, hidden_dim)
        cond_tokens = cond_tokens.unsqueeze(1)  # (B, 1, hidden_dim)
        cond_tokens = self.dropout(cond_tokens)

        # Pass through MMDiT blocks
        for block in self.mmdit_blocks:
            action_tokens, cond_tokens = block(action_tokens, cond_tokens)

        # Final output projection
        output = self.norm_out(action_tokens)
        output = self.proj_out(output)

        return output




class DiTPositionalEncoding(nn.Module):
    """Positional encoding for DiT based on dit-policy implementation."""
    def __init__(self, d_model, max_len=5000):
        super().__init__()
        # Compute the positional encodings once in log space
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(
            torch.arange(0, d_model, 2, dtype=torch.float)
            * -(np.log(10000.0) / d_model)
        )
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer("pe", pe)

    def forward(self, x):
        """
        Args:
            x: Tensor of shape (seq_len, batch_size, d_model)
        Returns:
            Tensor of shape (seq_len, batch_size, d_model) with positional encodings added
        """
        pe = self.pe[: x.shape[0]]
        pe = pe.repeat((1, x.shape[1], 1))
        return pe.detach().clone()


class DiTTimeNetwork(nn.Module):
    """Time embedding network based on dit-policy implementation."""
    def __init__(self, time_dim, out_dim, learnable_w=False):
        assert time_dim % 2 == 0, "time_dim must be even!"
        half_dim = int(time_dim // 2)
        super().__init__()

        w = np.log(10000) / (half_dim - 1)
        w = torch.exp(torch.arange(half_dim) * -w).float()
        self.register_parameter("w", nn.Parameter(w, requires_grad=learnable_w))

        self.out_net = nn.Sequential(
            nn.Linear(time_dim, out_dim), nn.SiLU(), nn.Linear(out_dim, out_dim)
        )

    def forward(self, x):
        assert len(x.shape) == 1, "assumes 1d input timestep array"
        x = x[:, None] * self.w[None]
        x = torch.cat((torch.cos(x), torch.sin(x)), dim=1)
        return self.out_net(x)


class DiTSelfAttnEncoder(nn.Module):
    """Self-attention encoder block based on dit-policy implementation."""
    def __init__(self, d_model, nhead=8, dim_feedforward=2048, dropout=0.1, activation="gelu"):
        super().__init__()
        self.self_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout)
        # Implementation of Feedforward model
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.linear2 = nn.Linear(dim_feedforward, d_model)

        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)

        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        self.dropout3 = nn.Dropout(dropout)

        self.activation = _get_activation_fn(activation)

    def forward(self, src, pos):
        q = k = _with_pos_embed(src, pos)
        src2, _ = self.self_attn(q, k, value=src, need_weights=False)
        src = src + self.dropout1(src2)
        src = self.norm1(src)
        src2 = self.linear2(self.dropout2(self.activation(self.linear1(src))))
        src = src + self.dropout3(src2)
        src = self.norm2(src)
        return src

    def reset_parameters(self):
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)


def _get_activation_fn(activation):
    """Return an activation function given a string"""
    if activation == "relu":
        return F.relu
    if activation == "gelu":
        return nn.GELU(approximate="tanh")
    if activation == "glu":
        return F.glu
    raise RuntimeError(f"activation should be relu/gelu/glu, not {activation}.")


def _with_pos_embed(tensor, pos=None):
    return tensor if pos is None else tensor + pos


class DiTShiftScaleMod(nn.Module):
    """Shift and scale modulation for DiT blocks."""
    def __init__(self, dim):
        super().__init__()
        self.act = nn.SiLU()
        self.scale = nn.Linear(dim, dim)
        self.shift = nn.Linear(dim, dim)

    def forward(self, x, c):
        c = self.act(c)
        return x * self.scale(c)[None] + self.shift(c)[None]

    def reset_parameters(self):
        nn.init.xavier_uniform_(self.scale.weight)
        nn.init.xavier_uniform_(self.shift.weight)
        nn.init.zeros_(self.scale.bias)
        nn.init.zeros_(self.shift.bias)


class DiTZeroScaleMod(nn.Module):
    """Zero-initialized scale modulation for DiT blocks."""
    def __init__(self, dim):
        super().__init__()
        self.act = nn.SiLU()
        self.scale = nn.Linear(dim, dim)

    def forward(self, x, c):
        c = self.act(c)
        return x * self.scale(c)[None]

    def reset_parameters(self):
        nn.init.zeros_(self.scale.weight)
        nn.init.zeros_(self.scale.bias)


class DiTDecoder(nn.Module):
    """DiT decoder block with conditioning modulation."""
    def __init__(self, d_model, nhead, dim_feedforward=2048, dropout=0.1, activation="gelu"):
        super().__init__()
        self.self_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout)
        # Implementation of Feedforward model
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.linear2 = nn.Linear(dim_feedforward, d_model)

        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)

        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        self.dropout3 = nn.Dropout(dropout)

        self.activation = _get_activation_fn(activation)

        # Create modulation layers
        self.attn_mod1 = DiTShiftScaleMod(d_model)
        self.attn_mod2 = DiTZeroScaleMod(d_model)
        self.mlp_mod1 = DiTShiftScaleMod(d_model)
        self.mlp_mod2 = DiTZeroScaleMod(d_model)

        self.cross_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout)

    def forward(self, x, t, cond):
        # 保存原始条件用于 cross-attention
        cond_seq = cond  # (seq_len, B, hidden_dim)

        # Process the conditioning vector for AdaLN (dit-policy style)
        cond_adaln = torch.mean(cond, axis=0)  # (B, hidden_dim)
        cond_adaln = cond_adaln + t

        # Self-attention with AdaLN modulation
        x2 = self.attn_mod1(self.norm1(x), cond_adaln)
        x2, _ = self.self_attn(x2, x2, x2, need_weights=False)
        x = self.attn_mod2(self.dropout1(x2), cond_adaln) + x

        # Cross-attention with condition sequence
        x_norm = self.norm2(x)
        x2, _ = self.cross_attn(x_norm, cond_seq, cond_seq, need_weights=False)
        x = x + self.dropout2(x2)  # 重要：恢复残差连接
        

        # MLP with AdaLN modulation
        x2 = self.mlp_mod1(self.norm3(x), cond_adaln)
        x2 = self.linear2(self.dropout3(self.activation(self.linear1(x2))))
        x2 = self.mlp_mod2(x2, cond_adaln)
        return x + x2

    def reset_parameters(self):
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

        for s in (self.attn_mod1, self.attn_mod2, self.mlp_mod1, self.mlp_mod2):
            s.reset_parameters()

        # Initialize cross-attention
        for p in self.cross_attn.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)


class DiTTransformerEncoder(nn.Module):
    """Transformer encoder for DiT observation processing."""
    def __init__(self, base_module, num_layers):
        super().__init__()
        self.layers = nn.ModuleList(
            [copy.deepcopy(base_module) for _ in range(num_layers)]
        )

        for l in self.layers:
            l.reset_parameters()

    def forward(self, src, pos):
        x, outputs = src, []
        for layer in self.layers:
            x = layer(x, pos)
            outputs.append(x)
        return outputs


class DiTTransformerDecoder(nn.Module):
    """Transformer decoder for DiT action generation."""
    def __init__(self, base_module, num_layers):
        super().__init__()
        self.layers = nn.ModuleList(
            [copy.deepcopy(base_module) for _ in range(num_layers)]
        )

        for l in self.layers:
            l.reset_parameters()

    def forward(self, src, t, all_conds):
        x = src
        # Handle case where encoder and decoder have different number of layers
        for i, layer in enumerate(self.layers):
            # Use the last encoder condition if we have more decoder layers than encoder layers
            cond_idx = min(i, len(all_conds) - 1)
            cond = all_conds[cond_idx]
            x = layer(x, t, cond)
        return x


class DiTForDiffusion(nn.Module):
    """
    Diffusion Transformer (DiT) for robot action diffusion.
    Based on dit-policy architecture with encoder-decoder structure and AdaLN modulation.
    """
    def __init__(self, config: DiffusionConfig, cond_dim: int):
        super().__init__()
        self.config = config
        self.hidden_dim = config.diffusion_step_embed_dim
        self.num_layers = config.n_layer
        self.num_heads = config.n_head
        self.n_obs_steps = config.n_obs_steps

        # Action sequence processing
        # Get action dimension from config (compatible with different config styles)
        if hasattr(config, 'action_feature') and hasattr(config.action_feature, 'shape'):
            input_dim = config.action_feature.shape[0]
        elif hasattr(config, 'output_shapes') and 'action' in config.output_shapes:
            input_dim = config.output_shapes['action'][0]
        else:
            # Default fallback
            input_dim = 7

        self.ac_dim = input_dim
        self.ac_chunk = config.horizon

        # Positional encoding blocks (dit-policy style)
        self.enc_pos = DiTPositionalEncoding(self.hidden_dim)
        self.register_parameter(
            "dec_pos",
            nn.Parameter(torch.empty(self.ac_chunk, 1, self.hidden_dim), requires_grad=True),
        )
        nn.init.xavier_uniform_(self.dec_pos.data)

        # Time embedding network (dit-policy style)
        time_dim = 256  # Standard time dimension
        self.time_net = DiTTimeNetwork(time_dim, self.hidden_dim)

        # Observation projection to hidden dimension
        single_cond_dim = cond_dim // self.n_obs_steps
        self.obs_proj = nn.Sequential(
            nn.Linear(single_cond_dim, single_cond_dim),
            nn.GELU(approximate="tanh"),
            nn.Linear(single_cond_dim, self.hidden_dim),
        )

        # Action projection (dit-policy style)
        self.ac_proj = nn.Sequential(
            nn.Linear(input_dim, input_dim),
            nn.GELU(approximate="tanh"),
            nn.Linear(input_dim, self.hidden_dim),
        )

        # Encoder blocks for observation processing
        encoder_module = DiTSelfAttnEncoder(
            self.hidden_dim,
            nhead=self.num_heads,
            dim_feedforward=self.hidden_dim * 4,
            dropout=config.p_drop_attn,
            activation="gelu",
        )
        self.encoder = DiTTransformerEncoder(encoder_module, self.num_layers)

        # Decoder blocks for action generation with conditioning
        decoder_module = DiTDecoder(
            self.hidden_dim,
            nhead=self.num_heads,
            dim_feedforward=self.hidden_dim * 4,
            dropout=config.p_drop_attn,
            activation="gelu",
        )
        self.decoder = DiTTransformerDecoder(decoder_module, self.num_layers)

        # Final output layer (dit-policy style)
        self.eps_out = DiTFinalLayer(self.hidden_dim, input_dim)

        # Initialize weights
        self.apply(self._init_weights)

        print(
            f"DiT diffusion parameters: {sum(p.numel() for p in self.parameters()):e}"
        )

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
            if module.weight is not None:
                torch.nn.init.ones_(module.weight)
        elif hasattr(module, 'action_pos_emb'):
            torch.nn.init.normal_(module.action_pos_emb, mean=0.0, std=0.02)

    def forward(self, sample: torch.Tensor, timestep: torch.Tensor, global_cond: torch.Tensor, **kwargs):
        """
        Args:
            sample: (B, T, action_dim) noisy action sequence
            timestep: (B,) diffusion timestep
            global_cond: (B, cond_dim) global conditioning (observations)
        Returns:
            (B, T, action_dim) predicted noise or denoised actions
        """
        # Reshape global_cond to separate observation steps
        # global_cond: (B, cond_dim) -> (B, n_obs_steps, single_cond_dim)
        batch_size = sample.shape[0]
        single_cond_dim = global_cond.shape[1] // self.n_obs_steps
        obs_enc = global_cond.view(batch_size, self.n_obs_steps, single_cond_dim)

        # Forward encoder (dit-policy style)
        enc_cache = self.forward_enc(obs_enc)

        # Forward decoder (dit-policy style)
        return self.forward_dec(sample, timestep, enc_cache)

    def forward_enc(self, obs_enc):
        """
        Encode observations using transformer encoder.
        Args:
            obs_enc: (B, n_obs_steps, cond_dim) observation encodings
        Returns:
            List of encoded observation features from each layer
        """
        # Project observations to hidden dimension
        obs_enc = self.obs_proj(obs_enc)  # (B, n_obs_steps, hidden_dim)

        # Transpose to (n_obs_steps, B, hidden_dim) for transformer
        obs_enc = obs_enc.transpose(0, 1)
        pos = self.enc_pos(obs_enc)
        enc_cache = self.encoder(obs_enc, pos)
        return enc_cache

    def forward_dec(self, noise_actions, time, enc_cache):
        """
        Decode actions using transformer decoder with conditioning.
        Args:
            noise_actions: (B, T, action_dim) noisy action sequence
            time: (B,) timestep
            enc_cache: List of encoded observation features
        Returns:
            (B, T, action_dim) predicted noise or denoised actions
        """
        time_enc = self.time_net(time)

        # Project actions to hidden dimension
        ac_tokens = self.ac_proj(noise_actions)  # (B, T, hidden_dim)
        ac_tokens = ac_tokens.transpose(0, 1)  # (T, B, hidden_dim)
        dec_in = ac_tokens + self.dec_pos[:ac_tokens.shape[0]]  # Add positional encoding

        # Apply decoder with conditioning from each encoder layer
        dec_out = self.decoder(dec_in, time_enc, enc_cache)

        # Apply final epsilon prediction layer
        return self.eps_out(dec_out, time_enc, enc_cache[-1])


class DiTFinalLayer(nn.Module):
    """
    Final layer for DiT with AdaLN modulation (dit-policy style).
    """
    def __init__(self, hidden_size, out_size):
        super().__init__()
        self.norm_final = nn.LayerNorm(hidden_size, elementwise_affine=False, eps=1e-6)
        self.linear = nn.Linear(hidden_size, out_size, bias=True)
        self.adaLN_modulation = nn.Sequential(
            nn.SiLU(), nn.Linear(hidden_size, 2 * hidden_size, bias=True)
        )

    def forward(self, x, t, cond):
        """
        Args:
            x: (T, B, hidden_dim) input tokens (transposed)
            t: (B, hidden_dim) time embedding
            cond: (T, B, hidden_dim) condition from last encoder layer
        Returns:
            (B, T, out_dim) output predictions
        """
        # Process the conditioning vector first (dit-policy style)
        cond = torch.mean(cond, axis=0)  # (B, hidden_dim)
        cond = cond + t  # (B, hidden_dim)

        shift, scale = self.adaLN_modulation(cond).chunk(2, dim=1)
        x = x * scale[None] + shift[None]  # Broadcast to (T, B, hidden_dim)
        x = self.linear(x)
        return x.transpose(0, 1)  # Return (B, T, out_dim)

    def reset_parameters(self):
        for p in self.parameters():
            nn.init.zeros_(p)


class MMDiTForDiffusion_DC(nn.Module):
    """
    MMDiT with SoftREPA-style DC (Discriminative/Contrastive) tokens for robot action diffusion.
    Supports soft prompt learning for domain adaptation and fine-tuning.
    """
    def __init__(self, config: DiffusionConfig, cond_dim: int, n_dc_tokens: int = 4, n_dc_layers: int = 6, use_dc_t: bool = True):
        super().__init__()
        self.config = config
        self.hidden_dim = config.diffusion_step_embed_dim
        self.num_layers = config.n_layer
        self.num_heads = config.n_head
        self.n_dc_tokens = n_dc_tokens
        self.n_dc_layers = min(n_dc_layers, self.num_layers)
        self.use_dc_t = use_dc_t

        # Action sequence processing
        input_dim = config.action_feature.shape[0]
        self.action_proj = nn.Linear(input_dim, self.hidden_dim)
        self.action_pos_emb = nn.Parameter(torch.zeros(1, config.horizon, self.hidden_dim))

        # Condition processing (observations + time)
        self.time_emb = DiffusionSinusoidalPosEmb(self.hidden_dim)
        self.cond_proj = nn.Linear(cond_dim, self.hidden_dim)

        # DC tokens for soft prompt learning (SoftREPA style)
        self.dc_tokens = nn.Parameter(torch.randn(self.n_dc_layers, self.n_dc_tokens, self.hidden_dim))
        if self.use_dc_t:
            # Time-dependent DC tokens (discretized timesteps)
            self.dc_t_tokens = nn.Embedding(100, self.hidden_dim * self.n_dc_layers)

        # MMDiT blocks with separate weights for action and condition
        self.mmdit_blocks = nn.ModuleList([
            MMDiTBlock(
                hidden_dim=self.hidden_dim,
                num_heads=self.num_heads,
                dropout=config.p_drop_attn
            ) for _ in range(self.num_layers)
        ])

        # Final output layer
        self.norm_out = nn.LayerNorm(self.hidden_dim)
        self.proj_out = nn.Linear(self.hidden_dim, input_dim)

        # Dropout
        self.dropout = nn.Dropout(config.p_drop_emb)

        # Initialize weights
        self.apply(self._init_weights)
        # Special initialization for DC tokens
        nn.init.normal_(self.dc_tokens, mean=0, std=0.02)
        if self.use_dc_t:
            nn.init.normal_(self.dc_t_tokens.weight, mean=0, std=0.02)

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
        elif hasattr(module, 'action_pos_emb'):
            torch.nn.init.normal_(module.action_pos_emb, mean=0.0, std=0.02)

    def initialize_dc(self, dc_tokens, dc_t_tokens=None):
        """Initialize DC tokens from pretrained weights (for transfer learning)"""
        self.dc_tokens.data = dc_tokens.type(self.dc_tokens.data.dtype)
        if dc_t_tokens is not None and self.use_dc_t:
            self.dc_t_tokens.weight.data = dc_t_tokens.type(self.dc_tokens.data.dtype)

    def forward(self, sample: torch.Tensor, timestep: torch.Tensor, global_cond: torch.Tensor, **kwargs):
        """
        Args:
            sample: (B, T, action_dim) noisy action sequence
            timestep: (B,) diffusion timestep
            global_cond: (B, cond_dim) global conditioning (observations)
        Returns:
            (B, T, action_dim) predicted noise or denoised actions
        """
        batch_size = sample.shape[0]

        # Process action tokens
        action_tokens = self.action_proj(sample)  # (B, T, hidden_dim)
        action_tokens = action_tokens + self.action_pos_emb[:, :action_tokens.shape[1], :]
        action_tokens = self.dropout(action_tokens)

        # Process condition tokens
        time_emb = self.time_emb(timestep)  # (B, hidden_dim)
        cond_tokens = self.cond_proj(global_cond)  # (B, hidden_dim)

        # Combine time and condition
        cond_tokens = cond_tokens + time_emb  # (B, hidden_dim)
        cond_tokens = cond_tokens.unsqueeze(1)  # (B, 1, hidden_dim)

        # Prepare time-dependent DC tokens if enabled
        dc_t_embs = None
        if self.use_dc_t:
            # Discretize timestep for embedding lookup
            int_t = (timestep * 99).long().clamp(0, 99)  # Map to [0, 99]
            dc_t_emb = self.dc_t_tokens(int_t)  # (B, hidden_dim * n_dc_layers)
            dc_t_embs = dc_t_emb.chunk(self.n_dc_layers, dim=-1)  # List of (B, hidden_dim)

        # Pass through MMDiT blocks with DC token injection
        for layer_idx, block in enumerate(self.mmdit_blocks):
            # Inject DC tokens for the first n_dc_layers
            if layer_idx < self.n_dc_layers:
                # Get base DC tokens for this layer
                dc_tokens = self.dc_tokens[layer_idx].expand(batch_size, -1, -1)  # (B, n_dc_tokens, hidden_dim)

                # Add time-dependent component if enabled
                if self.use_dc_t and dc_t_embs is not None:
                    dc_t_component = dc_t_embs[layer_idx].unsqueeze(1).expand(-1, self.n_dc_tokens, -1)
                    dc_tokens = dc_tokens + dc_t_component

                # Concatenate DC tokens with condition tokens
                enhanced_cond = torch.cat([dc_tokens, cond_tokens], dim=1)  # (B, n_dc_tokens + 1, hidden_dim)
            else:
                enhanced_cond = cond_tokens

            # Apply MMDiT block
            action_tokens, enhanced_cond = block(action_tokens, enhanced_cond)

            # Update condition tokens (remove DC tokens for next layer if they were added)
            if layer_idx < self.n_dc_layers:
                cond_tokens = enhanced_cond[:, self.n_dc_tokens:, :]  # Keep only original condition
            else:
                cond_tokens = enhanced_cond

        # Final output projection
        output = self.norm_out(action_tokens)
        output = self.proj_out(output)

        return output
